import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON>rowserRouter } from "react-router-dom";
import "./global.css";
import App from "./App.tsx";
import { ThemeProvider } from "./hooks/useTheme";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ThemeProvider defaultTheme="dark" storageKey="gemini-app-theme">
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </ThemeProvider>
  </StrictMode>
);
