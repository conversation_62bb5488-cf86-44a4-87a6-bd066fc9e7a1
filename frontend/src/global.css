@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.99 0.005 106);
  --foreground: oklch(0.15 0.02 258);
  --card: oklch(0.98 0.008 106);
  --card-foreground: oklch(0.15 0.02 258);
  --popover: oklch(0.98 0.008 106);
  --popover-foreground: oklch(0.15 0.02 258);
  --primary: oklch(0.47 0.15 258);
  --primary-foreground: oklch(0.98 0.008 106);
  --secondary: oklch(0.95 0.01 106);
  --secondary-foreground: oklch(0.15 0.02 258);
  --muted: oklch(0.95 0.01 106);
  --muted-foreground: oklch(0.45 0.05 258);
  --accent: oklch(0.93 0.015 106);
  --accent-foreground: oklch(0.15 0.02 258);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.88 0.02 106);
  --input: oklch(0.88 0.02 106);
  --ring: oklch(0.47 0.15 258);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.98 0.008 106);
  --sidebar-foreground: oklch(0.15 0.02 258);
  --sidebar-primary: oklch(0.47 0.15 258);
  --sidebar-primary-foreground: oklch(0.98 0.008 106);
  --sidebar-accent: oklch(0.93 0.015 106);
  --sidebar-accent-foreground: oklch(0.15 0.02 258);
  --sidebar-border: oklch(0.88 0.02 106);
  --sidebar-ring: oklch(0.47 0.15 258);
}

.dark {
  --background: oklch(0.08 0.01 258);
  --foreground: oklch(0.95 0.01 106);
  --card: oklch(0.12 0.015 258);
  --card-foreground: oklch(0.95 0.01 106);
  --popover: oklch(0.12 0.015 258);
  --popover-foreground: oklch(0.95 0.01 106);
  --primary: oklch(0.7 0.15 258);
  --primary-foreground: oklch(0.08 0.01 258);
  --secondary: oklch(0.18 0.02 258);
  --secondary-foreground: oklch(0.95 0.01 106);
  --muted: oklch(0.18 0.02 258);
  --muted-foreground: oklch(0.6 0.05 258);
  --accent: oklch(0.22 0.025 258);
  --accent-foreground: oklch(0.95 0.01 106);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.25 0.03 258);
  --input: oklch(0.25 0.03 258);
  --ring: oklch(0.7 0.15 258);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.12 0.015 258);
  --sidebar-foreground: oklch(0.95 0.01 106);
  --sidebar-primary: oklch(0.7 0.15 258);
  --sidebar-primary-foreground: oklch(0.08 0.01 258);
  --sidebar-accent: oklch(0.22 0.025 258);
  --sidebar-accent-foreground: oklch(0.95 0.01 106);
  --sidebar-border: oklch(0.25 0.03 258);
  --sidebar-ring: oklch(0.7 0.15 258);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Animation Delays */
.animation-delay-200 { animation-delay: 0.2s; }
.animation-delay-400 { animation-delay: 0.4s; }
.animation-delay-600 { animation-delay: 0.6s; }
.animation-delay-800 { animation-delay: 0.8s; }

/* Keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes fadeInUpSmooth {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}
.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out forwards;
}
.animate-fadeInUpSmooth {
  animation: fadeInUpSmooth 0.3s ease-out forwards;
}

/* Ensure your body or html has a dark background if not already set, e.g.: */
/* body { background-color: #0c0c0d; } */ /* This is similar to neutral-950 */
