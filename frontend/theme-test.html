<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题测试</title>
    <style>
        /* 浅色主题 */
        :root {
            --background: oklch(0.99 0.005 106);
            --foreground: oklch(0.15 0.02 258);
            --card: oklch(0.98 0.008 106);
            --card-foreground: oklch(0.15 0.02 258);
            --primary: oklch(0.47 0.15 258);
            --primary-foreground: oklch(0.98 0.008 106);
            --muted: oklch(0.95 0.01 106);
            --muted-foreground: oklch(0.45 0.05 258);
            --border: oklch(0.88 0.02 106);
        }

        /* 暗色主题 */
        .dark {
            --background: oklch(0.08 0.01 258);
            --foreground: oklch(0.95 0.01 106);
            --card: oklch(0.12 0.015 258);
            --card-foreground: oklch(0.95 0.01 106);
            --primary: oklch(0.7 0.15 258);
            --primary-foreground: oklch(0.08 0.01 258);
            --muted: oklch(0.18 0.02 258);
            --muted-foreground: oklch(0.6 0.05 258);
            --border: oklch(0.25 0.03 258);
        }

        body {
            background-color: var(--background);
            color: var(--foreground);
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 2rem;
            transition: background-color 0.3s, color 0.3s;
        }

        .card {
            background-color: var(--card);
            color: var(--card-foreground);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .button {
            background-color: var(--primary);
            color: var(--primary-foreground);
            border: none;
            border-radius: 0.375rem;
            padding: 0.5rem 1rem;
            cursor: pointer;
            margin: 0.5rem;
        }

        .muted {
            color: var(--muted-foreground);
        }

        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <button class="button" onclick="toggleTheme()">切换主题</button>
    </div>

    <h1>主题测试页面</h1>
    
    <div class="card">
        <h2>卡片标题</h2>
        <p>这是一个测试卡片，用来展示主题效果。</p>
        <p class="muted">这是一些次要文本内容。</p>
        <button class="button">主要按钮</button>
    </div>

    <div class="card">
        <h3>色彩展示</h3>
        <p>主要文本颜色</p>
        <p class="muted">次要文本颜色</p>
        <div style="background: var(--muted); padding: 1rem; border-radius: 0.25rem; margin: 0.5rem 0;">
            静音背景区域
        </div>
    </div>

    <script>
        function toggleTheme() {
            document.documentElement.classList.toggle('dark');
        }

        // 检测系统主题偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
    </script>
</body>
</html>
